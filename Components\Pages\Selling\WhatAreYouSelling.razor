@page "/what-are-you-selling"
@inject NavigationManager Navigation

<div class="selling-container">
    <!-- Title -->
    <h1 class="main-title">What are you selling?</h1>

    <!-- Category Grid -->
    <div class="category-grid">
        <!-- Books -->
        <div class="category-card books" @onclick="SelectBooks">
            <div class="category-icon">
                ??

            </div>
            <span class="category-label">Books</span>
        </div>

        <!-- Electronics -->
        <div class="category-card electronics" @onclick="SelectElectronics">
            <div class="category-icon">
                ??

            </div>
            <span class="category-label">Electronics</span>
        </div>

        <!-- Clothing -->
        <div class="category-card clothing" @onclick="SelectClothing">
            <div class="category-icon">
                ??

            </div>
            <span class="category-label">Clothing</span>
        </div>

        <!-- Other -->
        <div class="category-card other" @onclick="SelectOther">
            <div class="category-icon">
                ??

            </div>
            <span class="category-label">Other</span>
        </div>
    </div>

    <!-- Next Button -->
    <button class="next-button" @onclick="GoNext">Next</button>
</div>

<style>
    .selling-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px 16px 20px 16px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        min-height: 100vh;
        justify-content: flex-start;
        max-width: 400px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        background-color: #FFFFFF;
    }

    .main-title {
        font-size: 24px;
        font-weight: 700;
        color: #000000;
        text-align: center;
        margin-bottom: 16px;
        line-height: 1.2;
    }

    .category-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        width: 100%;
        max-width: 320px;
        margin-bottom: 40px;
    }

    .category-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        border-radius: 20px;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        min-height: 120px;
        box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
    }

        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.15);
        }

        .category-card:active {
            transform: translateY(0);
        }

        /* Flat background colors to match screenshot */
        .category-card.books {
            background-color: #42A5F5;
        }

        .category-card.electronics {
            background-color: #66BB6A;
        }

        .category-card.clothing {
            background-color: #D7A57F;
        }

        .category-card.other {
            background-color: #EF5350;
        }

    .category-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .category-label {
        color: white;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        line-height: 1.2;
    }

    .category-icon {
        font-size: 24px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
    }


    .next-button {
        width: 100%;
        max-width: 320px;
        padding: 16px;
        background-color: #B3E5FC;
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
        margin-top: auto;
    }

        .next-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
        }

        .next-button:active {
            transform: translateY(0);
        }

    /* Mobile Responsiveness - Responsive styles applied via CSS classes */
</style>

@code {

    private string selectedCategory = "";

    private void SelectBooks()
    {
        selectedCategory = "Books";
        StateHasChanged();
    }

    private void SelectElectronics()
    {
        selectedCategory = "Electronics";
        StateHasChanged();
    }

    private void SelectClothing()
    {
        selectedCategory = "Clothing";
        StateHasChanged();
    }

    private void SelectOther()
    {
        selectedCategory = "Other";
        StateHasChanged();
    }

    private void GoNext()
    {

        Navigation.NavigateTo("/add-photos-videos");
    }
}
