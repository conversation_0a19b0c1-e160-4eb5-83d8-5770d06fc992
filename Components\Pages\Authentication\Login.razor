@page "/login"
@inject NavigationManager Navigation

<div class="login-container">
    <!-- Logo Section -->
    <div class="logo-section">
        <img src="/images/gatesale-logo.png" alt="GATESALE Logo" class="logo-image" />
    </div>

    <!-- Form Section -->
    <div class="form-section">
        <!-- School Email Address -->
        <div class="input-group">
            <label class="input-label">School Email Address</label>
            <div class="input-wrapper">
                <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M22 6L12 13L2 6" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <input type="email" class="form-input" placeholder="<EMAIL>" @bind="Email" />
            </div>
        </div>

        <!-- Password -->
        <div class="input-group">
            <label class="input-label">Password</label>
            <div class="input-wrapper">
                <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="#AAAAAA" stroke-width="2"/>
                    <circle cx="12" cy="16" r="1" fill="#AAAAAA"/>
                    <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="#AAAAAA" stroke-width="2"/>
                </svg>
                <input type="@(ShowPassword ? "text" : "password")" class="form-input" placeholder="Enter password" @bind="Password" />
                <button type="button" class="password-toggle" @onclick="TogglePasswordVisibility">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        @if (ShowPassword)
                        {
                            <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.02811 7.65663 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4282 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5718 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L23 23" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        }
                        else
                        {
                            <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="12" r="3" stroke="#AAAAAA" stroke-width="2"/>
                        }
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Login Button -->
    <button class="login-button" @onclick="HandleLogin">
        Login <span class="next-icon">→</span>
    </button>

    <!-- Sign Up Link -->
    <p class="signup-text">
        Don't have an account? <a href="/signup" class="signup-link">Sign Up</a>
    </p>
</div>

<style>
    .login-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
        min-height: 100vh;
        max-width: 375px;
        width: 100%;
        margin: 0 auto;
        position: relative;
        box-sizing: border-box;
    }

    .logo-section {
        margin-top: 80px;
        margin-bottom: 120px;
        display: flex;
        justify-content: center;
    }

    .logo-image {
        width: 165px;
        height: 121px;
        object-fit: contain;
    }

    .form-section {
        width: 100%;
        max-width: 320px;
        display: flex;
        flex-direction: column;
        gap: 24px;
        margin-bottom: 40px;
    }

    .input-group {
        display: flex;
        flex-direction: column;
    }

    .input-label {
        font-size: 14px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8px;
    }

    .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-icon {
        position: absolute;
        left: 16px;
        z-index: 2;
    }

    .form-input {
        width: 100%;
        height: 48px;
        padding: 12px 16px 12px 48px;
        border: 1px solid #E0E0E0;
        border-radius: 8px;
        font-size: 16px;
        color: #333333;
        background-color: #FAFAFA;
        box-sizing: border-box;
    }

    .form-input:focus {
        outline: none;
        border-color: #00BFFF;
        box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.1);
        background-color: white;
    }

    .form-input::placeholder {
        color: #AAAAAA;
    }

    .password-toggle {
        position: absolute;
        right: 16px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
    }

    .password-toggle:hover {
        opacity: 0.7;
    }

    .login-button {
        width: 100%;
        max-width: 320px;
        height: 50px;
        background-color: #00BFFF;
        color: white;
        border: none;
        border-radius: 15px;
        font-size: 20px;
        font-weight: 600;
        cursor: pointer;
        margin-bottom: 24px;
        box-shadow: 0 3px 8px rgba(0, 191, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .next-icon {
        font-size: 28px; /* You can increase this size as needed */
        font-weight: bold;
        line-height: 1;
        margin-bottom: 5px;
    }
    

    

    .signup-text {
        font-size: 14px;
        color: #666666;
        text-align: center;
        margin: 0;
    }

    .signup-link {
        color: #00BFFF;
        text-decoration: none;
        font-weight: 600;
    }

    .signup-link:hover {
        text-decoration: underline;
    }
</style>

@code {
    private string Email = "";
    private string Password = "";
    private bool ShowPassword = false;

    private void TogglePasswordVisibility()
    {
        ShowPassword = !ShowPassword;
    }

    private void HandleLogin()
    {
        // Handle login logic here
        // For now, navigate to main page
        Navigation.NavigateTo("/AddSchool");
    }
}
