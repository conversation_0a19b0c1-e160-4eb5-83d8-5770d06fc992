@page "/add-photos-videos"
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="add-photos-container">
    <!-- Header -->
    <div class="header-section">
        <button class="back-button" @onclick="GoBack">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
        <h1 class="page-title">Add Photos or Videos</h1>
    </div>

    <!-- Camera Illustration -->
    <div class="camera-img">
        <img src="/images/camera.jpg" alt="GATESALE Logo" class="logo-image" />

    </div>

    <!-- Upload Section -->
    <div class="upload-section">
        <div class="upload-options">
            <button class="upload-option" @onclick="OpenCamera">
                <div class="option-icon camera-icon-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M23 19C23 19.5304 22.7893 20.0391 22.4142 20.4142C22.0391 20.7893 21.5304 21 21 21H3C2.46957 21 1.96086 20.7893 1.58579 20.4142C1.21071 20.0391 1 19.5304 1 19V8C1 7.46957 1.21071 6.96086 1.58579 6.58579C1.96086 6.21071 2.46957 6 3 6H7L9 4H15L17 6H21C21.5304 6 22.0391 6.21071 22.4142 6.58579C22.7893 6.96086 23 7.46957 23 8V19Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <circle cx="12" cy="13" r="4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="option-label">Camera</span>
            </button>

            <button class="upload-option" @onclick="OpenGallery">
                <div class="option-icon gallery-icon-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="white" stroke-width="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5" stroke="white" stroke-width="2"/>
                        <path d="M21 15L16 10L5 21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="option-label">Gallery</span>
            </button>
        </div>

        <div class="upload-text">
            <p class="primary-text">Tap to add photos or videos</p>
            <p class="secondary-text">or drag and drop files here</p>
        </div>
    </div>

    <!-- Selected Media Counter -->
    <div class="media-counter">
        <span>Selected Media (@selectedFiles.Count/5)</span>
    </div>

    <!-- Media Grid -->
    <div class="media-grid">
        @for (int i = 0; i < 6; i++)
        {
            <div class="media-slot @(i < selectedFiles.Count ? "filled" : "empty")" @onclick="() => AddMediaSlot(i)">
                @if (i < selectedFiles.Count)
                {
                    <img src="@selectedFiles[i].PreviewUrl" alt="Selected media" class="media-preview" />
                    <button class="remove-media" @onclick="() => RemoveMedia(i)" @onclick:stopPropagation="true">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M18 6L6 18M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                }
                else
                {
                    <div class="add-media-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5V19M5 12H19" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                }
            </div>
        }
    </div>

    <!-- Instructions -->
    <div class="instructions">
        <span>Hold and drag to reorder • Tap X to remove</span>
    </div>

    <!-- Tips Section -->
    <div class="tips-section">
        <div class="tips-header">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" fill="#FF9800"/>
                <path d="M12 16V12M12 8H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span class="tips-title">Tips for great photos</span>
        </div>
        <ul class="tips-list">
            <li>Use good lighting</li>
            <li>Keep images clear and focused</li>
            <li>Max file size: 10MB each</li>
        </ul>
    </div>

    <!-- Next Button -->
    <button class="next-button" @onclick="GoNext" disabled="@(selectedFiles.Count == 0)">Next</button>
</div>

<style>
    .add-photos-container {
        display: flex;
        flex-direction: column;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        min-height: 100vh;
        background-color: #F8F9FA;
        max-width: 400px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
    }

    .header-section {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        gap: 16px;
    }

    .back-button {
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        border-radius: 8px;
        transition: background-color 0.2s ease;
    }

    .back-button:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .page-title {
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        margin: 0;
    }

    .camera-illustration {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
        position: relative;
    }

    .camera-icon {
        position: relative;
    }

    .sparkle {
        position: absolute;
        font-size: 16px;
        animation: sparkle 2s ease-in-out infinite;
    }

    .sparkle-1 {
        top: -10px;
        left: -15px;
        animation-delay: 0s;
    }

    .sparkle-2 {
        top: -5px;
        right: -20px;
        animation-delay: 0.7s;
    }

    .sparkle-3 {
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 1.4s;
    }

    @@keyframes sparkle {
        0%, 100% { opacity: 0.3; transform: scale(0.8); }
        50% { opacity: 1; transform: scale(1.2); }
    }

    .upload-section {
        background: white;
        border: 2px dashed #42A5F5;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 20px;
        text-align: center;
    }

    .upload-options {
        display: flex;
        justify-content: center;
        gap: 32px;
        margin-bottom: 20px;
    }

    .upload-option {
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .option-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #42A5F5;
        transition: transform 0.2s ease;
    }

    .upload-option:hover .option-icon {
        transform: scale(1.05);
    }

    .option-label {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
    }

    .upload-text {
        color: #666666;
    }

    .primary-text {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 4px 0;
        color: #333333;
    }

    .secondary-text {
        font-size: 14px;
        margin: 0;
        color: #9CA3AF;
    }

    .media-counter {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 16px;
    }

    .media-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        margin-bottom: 16px;
    }

    .media-slot {
        aspect-ratio: 1;
        border-radius: 8px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.2s ease;
    }

    .media-slot.empty {
        background: white;
        border: 1px solid #E5E7EB;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .media-slot.empty:hover {
        border-color: #42A5F5;
        background: #F8FAFF;
    }

    .media-slot.filled {
        background: #F3F4F6;
    }

    .media-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .remove-media {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.6);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .add-media-icon {
        color: #9CA3AF;
    }

    .instructions {
        text-align: center;
        font-size: 12px;
        color: #9CA3AF;
        margin-bottom: 20px;
    }

    .tips-section {
        background: #FFF3E0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
    }

    .tips-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
    }

    .tips-title {
        font-size: 14px;
        font-weight: 600;
        color: #E65100;
    }

    .tips-list {
        margin: 0;
        padding-left: 16px;
        color: #BF360C;
        font-size: 13px;
    }

    .tips-list li {
        margin-bottom: 4px;
    }

    .next-button {
        width: 100%;
        padding: 16px;
        background: linear-gradient(135deg, #81D4FA 0%, #4FC3F7 100%);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-top: auto;
    }

    .next-button:disabled {
        background: #E5E7EB;
        color: #9CA3AF;
        cursor: not-allowed;
    }

    .next-button:not(:disabled):hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 195, 247, 0.4);
    }
</style>

@code {
    private List<MediaFile> selectedFiles = new List<MediaFile>();

    public class MediaFile
    {
        public string Name { get; set; } = "";
        public string PreviewUrl { get; set; } = "";
        public long Size { get; set; }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/what-are-you-selling");
    }

    private async Task OpenCamera()
    {
        // Simulate camera functionality
        if (selectedFiles.Count < 5)
        {
            var newFile = new MediaFile
            {
                Name = $"Camera_Photo_{selectedFiles.Count + 1}.jpg",
                PreviewUrl = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA3MEM2MS4wNDU3IDcwIDcwIDYxLjA0NTcgNzAgNTBDNzAgMzguOTU0MyA2MS4wNDU3IDMwIDUwIDMwQzM4Ljk1NDMgMzAgMzAgMzguOTU0MyAzMCA1MEMzMCA2MS4wNDU3IDM4Ljk1NDMgNzAgNTAgNzBaIiBmaWxsPSIjNDJBNUY1Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+UGhvdG88L3RleHQ+Cjwvc3ZnPgo=",
                Size = 2048000 // 2MB
            };
            selectedFiles.Add(newFile);
            StateHasChanged();
        }
    }

    private async Task OpenGallery()
    {
        // Simulate gallery functionality
        if (selectedFiles.Count < 5)
        {
            var newFile = new MediaFile
            {
                Name = $"Gallery_Image_{selectedFiles.Count + 1}.jpg",
                PreviewUrl = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA3MEM2MS4wNDU3IDcwIDcwIDYxLjA0NTcgNzAgNTBDNzAgMzguOTU0MyA2MS4wNDU3IDMwIDUwIDMwQzM4Ljk1NDMgMzAgMzAgMzguOTU0MyAzMCA1MEMzMCA2MS4wNDU3IDM4Ljk1NDMgNzAgNTAgNzBaIiBmaWxsPSIjNjZCQjZBIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+SW1hZ2U8L3RleHQ+Cjwvc3ZnPgo=",
                Size = 1536000 // 1.5MB
            };
            selectedFiles.Add(newFile);
            StateHasChanged();
        }
    }

    private void AddMediaSlot(int index)
    {
        if (index >= selectedFiles.Count && selectedFiles.Count < 5)
        {
            // Open file picker or show options
            OpenGallery();
        }
    }

    private void RemoveMedia(int index)
    {
        if (index < selectedFiles.Count)
        {
            selectedFiles.RemoveAt(index);
            StateHasChanged();
        }
    }

    private void GoNext()
    {
        if (selectedFiles.Count > 0)
        {
            Navigation.NavigateTo("/selling/details");
        }
    }
}
